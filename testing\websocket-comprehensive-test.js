#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const WebSocketClient = require('./lib/websocket-client');
const ResponseValidator = require('./lib/validators');

class WebSocketComprehensiveTest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    this.websocket = null;
    this.user = null;
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA WebSocket Comprehensive Test');
    
    try {
      // Setup test user
      await this.setupTestUser();

      // Test WebSocket connection
      await this.testWebSocketConnection();

      // Test WebSocket authentication
      await this.testWebSocketAuthentication();

      // Test notification structure validation
      await this.testNotificationStructures();

      // Test WebSocket with assessment flow
      await this.testWebSocketAssessmentFlow();

      // Test connection management
      await this.testConnectionManagement();

      // Summary
      this.printTestSummary();

    } catch (error) {
      this.logger.error('Test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  async setupTestUser() {
    this.logger.step(1, 6, 'Setting up test user');
    
    this.user = this.testData.generateTestUser(1);
    
    // Register user
    const registerResponse = await this.api.register({
      email: this.user.email,
      password: this.user.password,
      username: this.user.username
    });

    this.user.id = registerResponse.data.user.id;
    this.user.token = registerResponse.data.token;
    this.api.setAuthToken(this.user.token);
    
    this.logger.success('Test user setup completed');
  }

  async testWebSocketConnection() {
    this.logger.step(2, 6, 'Testing WebSocket connection');
    
    try {
      this.websocket = new WebSocketClient(null, this.logger);
      
      // Test connection to API Gateway (recommended)
      await this.testConnection('API Gateway WebSocket', 'http://localhost:3000');
      
      this.logger.success('WebSocket connection test completed');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('WebSocket connection test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`WebSocket connection: ${error.message}`);
    }
    this.testResults.total++;
  }

  async testConnection(name, url) {
    this.logger.info(`Testing ${name} connection: ${url}`);
    
    const ws = new WebSocketClient(url, this.logger);
    
    try {
      await ws.connect();
      this.logger.success(`${name} connection successful`);
      
      const state = ws.getConnectionState();
      this.logger.info(`Connection state: ${JSON.stringify(state, null, 2)}`);
      
      ws.disconnect();
      
    } catch (error) {
      this.logger.error(`${name} connection failed:`, error.message);
      throw error;
    }
  }

  async testWebSocketAuthentication() {
    this.logger.step(3, 6, 'Testing WebSocket authentication');
    
    try {
      // Test authentication with valid token
      await this.testAuthenticationScenario('Valid token', this.user.token, true);
      
      // Test authentication with invalid token
      await this.testAuthenticationScenario('Invalid token', 'invalid.token.here', false);
      
      // Test authentication timeout
      await this.testAuthenticationTimeout();
      
      this.logger.success('WebSocket authentication test completed');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('WebSocket authentication test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`WebSocket auth: ${error.message}`);
    }
    this.testResults.total++;
  }

  async testAuthenticationScenario(name, token, shouldSucceed) {
    this.logger.info(`Testing authentication: ${name}`);
    
    const ws = new WebSocketClient(null, this.logger);
    
    try {
      await ws.connect();
      
      if (shouldSucceed) {
        await ws.authenticate(token);
        this.logger.success(`${name}: Authentication successful`);
        
        const state = ws.getConnectionState();
        if (!state.isAuthenticated) {
          throw new Error('Authentication state not updated correctly');
        }
        
      } else {
        try {
          await ws.authenticate(token);
          throw new Error(`${name}: Should have failed but succeeded`);
        } catch (authError) {
          this.logger.success(`${name}: Authentication correctly failed`);
        }
      }
      
    } finally {
      ws.disconnect();
    }
  }

  async testAuthenticationTimeout() {
    this.logger.info('Testing authentication timeout');
    
    const ws = new WebSocketClient(null, this.logger);
    ws.authTimeout = 2000; // Set short timeout for testing
    
    try {
      await ws.connect();
      
      // Don't send authentication - should timeout
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          const state = ws.getConnectionState();
          if (state.isAuthenticated) {
            reject(new Error('Should not be authenticated after timeout'));
          } else {
            this.logger.success('Authentication timeout test passed');
            resolve();
          }
        }, 3000);
      });
      
    } finally {
      ws.disconnect();
    }
  }

  async testNotificationStructures() {
    this.logger.step(4, 6, 'Testing notification structures');
    
    try {
      // Test notification structure validation according to WebSocket manual
      const testNotifications = {
        'analysis-started': {
          jobId: 'job_test-123',
          status: 'started',
          message: 'Your analysis has started processing...',
          metadata: {
            assessmentName: 'AI-Driven Talent Mapping',
            estimatedProcessingTime: '5-10 minutes'
          },
          timestamp: new Date().toISOString()
        },
        'analysis-complete': {
          jobId: 'job_test-123',
          resultId: 'result_test-123',
          status: 'completed',
          message: 'Your analysis is ready!',
          metadata: {
            assessmentName: 'AI-Driven Talent Mapping',
            processingTime: '7 minutes'
          },
          timestamp: new Date().toISOString()
        },
        'analysis-failed': {
          jobId: 'job_test-123',
          error: 'Processing error occurred',
          message: 'Analysis failed. Please try again.',
          metadata: {
            assessmentName: 'AI-Driven Talent Mapping',
            errorType: 'PROCESSING_ERROR'
          },
          timestamp: new Date().toISOString()
        }
      };

      for (const [type, notification] of Object.entries(testNotifications)) {
        this.logger.info(`Validating ${type} notification structure`);
        
        const validation = this.validator.validateWebSocketNotification(notification, type);
        if (!validation.isValid) {
          throw new Error(`${type} validation failed: ${validation.errors.join(', ')}`);
        }
        
        // Test WebSocket client validation
        const isValid = this.websocket.validateNotificationStructure(notification, type);
        if (!isValid) {
          throw new Error(`${type} WebSocket client validation failed`);
        }
        
        this.logger.success(`${type} notification structure valid`);
      }
      
      this.logger.success('Notification structures test completed');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Notification structures test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Notification structures: ${error.message}`);
    }
    this.testResults.total++;
  }

  async testWebSocketAssessmentFlow() {
    this.logger.step(5, 6, 'Testing WebSocket assessment flow');
    
    try {
      // Connect and authenticate WebSocket
      this.websocket = new WebSocketClient(null, this.logger);
      await this.websocket.connectAndAuthenticate(this.user.token);
      
      // Submit assessment
      const assessmentResponse = await this.api.submitAssessment(this.user.assessmentData);
      this.user.jobId = assessmentResponse.data.jobId;
      
      this.logger.info('Assessment submitted, waiting for notifications...');
      
      // Wait for assessment completion with timeout
      const timeout = parseInt(process.env.ASSESSMENT_TIMEOUT) || 300000;
      
      try {
        const result = await this.websocket.waitForAssessmentCompletion(timeout);
        this.user.resultId = result.resultId;
        
        this.logger.success('Assessment flow completed successfully');
        this.logger.info(`Result ID: ${this.user.resultId}`);
        
        // Validate all received notifications
        const notifications = this.websocket.getNotifications();
        this.logger.info(`Total notifications received: ${notifications.length}`);
        
        for (const notif of notifications) {
          const validation = this.validator.validateWebSocketNotification(notif.data, notif.type);
          if (!validation.isValid) {
            this.logger.warn(`Notification validation failed for ${notif.type}: ${validation.errors.join(', ')}`);
          } else {
            this.logger.success(`Notification ${notif.type} structure valid`);
          }
        }
        
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error('Assessment flow failed:', error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`Assessment flow: ${error.message}`);
      }
      
    } catch (error) {
      this.logger.error('WebSocket assessment flow test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`WebSocket assessment flow: ${error.message}`);
    }
    this.testResults.total++;
  }

  async testConnectionManagement() {
    this.logger.step(6, 6, 'Testing connection management');
    
    try {
      // Test reconnection
      this.logger.info('Testing reconnection...');
      
      if (this.websocket && this.websocket.socket) {
        // Simulate disconnect
        this.websocket.socket.disconnect();
        
        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Reconnect and re-authenticate
        await this.websocket.connectAndAuthenticate(this.user.token);
        
        this.logger.success('Reconnection test passed');
      }
      
      this.logger.success('Connection management test completed');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Connection management test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Connection management: ${error.message}`);
    }
    this.testResults.total++;
  }

  async cleanup() {
    this.logger.info('Performing cleanup...');
    
    try {
      // Disconnect WebSocket
      if (this.websocket) {
        this.websocket.disconnect();
        this.logger.info('WebSocket disconnected');
      }

      if (this.user && this.user.token) {
        this.api.setAuthToken(this.user.token);
        
        // Delete assessment result if exists
        if (this.user.resultId) {
          try {
            await this.api.deleteResult(this.user.resultId);
            this.logger.info('Assessment result deleted');
          } catch (error) {
            this.logger.warn('Failed to delete assessment result:', error.message);
          }
        }

        // Logout user
        try {
          await this.api.logout();
          this.logger.info('User logged out');
        } catch (error) {
          this.logger.warn('Failed to logout user:', error.message);
        }
      }

      this.api.setAuthToken(null);
      this.logger.success('Cleanup completed');
      
    } catch (error) {
      this.logger.error('Cleanup failed:', error.message);
    }
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('WEBSOCKET COMPREHENSIVE TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All WebSocket tests passed!');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} test(s) failed. Please check the errors above.`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const test = new WebSocketComprehensiveTest();
  test.runTest().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = WebSocketComprehensiveTest;
