# ATMA E2E Testing Guide

## Overview

This comprehensive testing suite validates the complete ATMA (AI-Driven Talent Mapping Assessment) backend system through end-to-end testing scenarios. It covers all major user flows, API endpoints, WebSocket functionality, and system integrations.

## Test Architecture

### Core Components

1. **Logger** (`lib/logger.js`)
   - Structured logging with timestamps
   - Multiple log levels (error, warn, info, debug)
   - File-based logging for test history
   - Colored console output for readability

2. **API Client** (`lib/api-client.js`)
   - HTTP client with automatic request/response logging
   - JWT token management
   - Response validation and saving
   - Comprehensive endpoint coverage

3. **WebSocket Client** (`lib/websocket-client.js`)
   - Real-time notification handling
   - Connection state management
   - Authentication flow
   - Notification validation

4. **Test Data Generator** (`lib/test-data.js`)
   - Random user data generation
   - Assessment data creation
   - Validation helpers
   - Configurable test scenarios

5. **Response Validator** (`lib/validators.js`)
   - Comprehensive response validation
   - Schema validation for all API responses
   - WebSocket notification validation
   - Error detection and reporting

### Test Suites

1. **Complete E2E Test** (`test-runner.js`)
   - Full user journey testing
   - Dual user parallel execution
   - Comprehensive validation
   - Automatic cleanup

2. **Single User Test** (`single-user-test.js`)
   - Focused single user flow
   - Detailed step-by-step validation
   - Simplified debugging

3. **Dual User Test** (`dual-user-test.js`)
   - Parallel user execution
   - Concurrency testing
   - Resource contention validation

4. **WebSocket Test** (`websocket-test.js`)
   - Real-time notification testing
   - Connection management
   - Reconnection handling
   - Notification validation

5. **Chatbot Test** (`chatbot-test.js`)
   - AI conversation testing
   - Assessment integration
   - Message validation
   - Context preservation

## Test Flow Details

### 1. User Registration Flow
```
Generate random email → Register user → Validate response → Store credentials
```
- **Validation Points**: Response structure, user object, JWT token format
- **Error Handling**: Duplicate email, invalid data, server errors

### 2. Authentication Flow
```
Login with credentials → Validate JWT token → Set authorization header
```
- **Validation Points**: Token format, expiration, user data consistency
- **Error Handling**: Invalid credentials, expired tokens, rate limiting

### 3. WebSocket Connection Flow
```
Connect to WebSocket → Authenticate with JWT → Join user room → Listen for events
```
- **Validation Points**: Connection state, authentication success, room joining
- **Error Handling**: Connection failures, authentication timeouts, reconnection

### 4. Profile Management Flow
```
Get current profile → Update profile data → Validate changes → Verify persistence
```
- **Validation Points**: Profile structure, data validation, update confirmation
- **Error Handling**: Invalid data, authorization failures, validation errors

### 5. Assessment Submission Flow
```
Submit assessment data → Receive job ID → Wait for processing → Get notifications
```
- **Validation Points**: Job creation, status tracking, notification delivery
- **Error Handling**: Invalid assessment data, processing failures, timeouts

### 6. Real-time Notification Flow
```
Submit assessment → Receive started notification → Wait for completion → Validate result
```
- **Validation Points**: Notification structure, timing, data consistency
- **Error Handling**: Missing notifications, invalid data, timeout scenarios

### 7. Persona Profile Flow
```
Assessment completion → Retrieve persona profile → Validate AI-generated content
```
- **Validation Points**: Persona structure, career recommendations, insights quality
- **Error Handling**: Missing results, invalid persona data, incomplete processing

### 8. Chatbot Integration Flow
```
Create conversation → Send messages → Receive AI responses → Validate context
```
- **Validation Points**: Conversation creation, message structure, AI response quality
- **Error Handling**: Context loss, invalid responses, conversation failures

### 9. Cleanup Flow
```
Delete conversations → Delete assessment results → Logout user → Clear resources
```
- **Validation Points**: Successful deletion, resource cleanup, session termination
- **Error Handling**: Deletion failures, orphaned resources, cleanup errors

## Validation Framework

### Response Validation
- **Structure Validation**: Required fields, data types, format compliance
- **Business Logic Validation**: Data consistency, relationship integrity
- **Security Validation**: Token format, authorization checks, data sanitization

### WebSocket Validation
- **Connection Validation**: State management, authentication flow
- **Notification Validation**: Event structure, timing, data integrity
- **Error Handling**: Connection failures, message loss, reconnection logic

### Data Validation
- **Assessment Data**: Score ranges, required fields, data completeness
- **User Data**: Email format, password strength, profile completeness
- **AI Generated Data**: Persona quality, recommendation relevance, insight accuracy

## Error Handling Strategy

### Graceful Degradation
- Continue testing when non-critical errors occur
- Collect all errors for comprehensive reporting
- Attempt cleanup even when tests fail

### Error Classification
- **Critical Errors**: Stop test execution (authentication failures, service unavailable)
- **Warning Errors**: Continue with logging (validation failures, timeout warnings)
- **Info Errors**: Log for analysis (performance issues, minor inconsistencies)

### Recovery Mechanisms
- **Automatic Retry**: For transient network issues
- **Fallback Options**: Alternative test paths when primary fails
- **Emergency Cleanup**: Resource cleanup on catastrophic failures

## Performance Monitoring

### Timing Metrics
- **Request Response Time**: HTTP API call duration
- **WebSocket Latency**: Real-time notification delivery time
- **Assessment Processing Time**: AI analysis completion duration
- **End-to-End Flow Time**: Complete user journey duration

### Resource Monitoring
- **Memory Usage**: Test client memory consumption
- **Connection Count**: Active WebSocket connections
- **API Rate Limiting**: Request rate compliance
- **Error Rates**: Failure percentage tracking

## Configuration Management

### Environment Variables
```env
# Core Configuration
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000
TEST_TIMEOUT=30000
ASSESSMENT_TIMEOUT=300000

# Test Data Configuration
TEST_EMAIL_DOMAIN=test.atma.local
TEST_PASSWORD=TestPassword123!
DEFAULT_ASSESSMENT_NAME=E2E Test Assessment

# Debug Configuration
DEBUG_MODE=true
LOG_LEVEL=info
SAVE_RESPONSES=true
```

### Test Customization
- **User Count**: Configure number of test users
- **Assessment Types**: Different assessment configurations
- **Timeout Values**: Adjust for different environments
- **Validation Levels**: Control validation strictness

## Reporting and Analytics

### Test Reports
- **Summary Report**: Pass/fail counts, success rates, timing
- **Detailed Report**: Step-by-step execution logs
- **Error Report**: Comprehensive error analysis
- **Performance Report**: Timing and resource usage metrics

### Log Analysis
- **Structured Logging**: JSON format for easy parsing
- **Searchable Logs**: Grep-friendly log format
- **Historical Tracking**: Test execution history
- **Trend Analysis**: Performance and reliability trends

## Best Practices

### Test Design
- **Isolation**: Each test should be independent
- **Repeatability**: Tests should produce consistent results
- **Cleanup**: Always clean up test data
- **Validation**: Validate all responses and state changes

### Error Handling
- **Comprehensive**: Catch and handle all error types
- **Informative**: Provide detailed error messages
- **Recoverable**: Attempt recovery when possible
- **Logged**: Record all errors for analysis

### Performance
- **Efficient**: Minimize resource usage
- **Parallel**: Use parallel execution when safe
- **Timeout**: Set appropriate timeouts
- **Monitoring**: Track performance metrics

### Maintenance
- **Documentation**: Keep documentation current
- **Refactoring**: Regularly improve test code
- **Updates**: Keep up with API changes
- **Monitoring**: Watch for test reliability issues

## Troubleshooting Guide

### Common Issues and Solutions

1. **Service Connection Issues**
   ```bash
   # Check service health
   curl http://localhost:3000/api/health
   
   # Verify WebSocket service
   curl http://localhost:3000/api/notifications/health
   ```

2. **Authentication Problems**
   ```bash
   # Check token format in logs
   grep "JWT" testing/results/logs/*.log
   
   # Verify token expiration
   node -e "console.log(JSON.parse(Buffer.from('TOKEN_PAYLOAD', 'base64')))"
   ```

3. **WebSocket Connection Issues**
   ```bash
   # Test WebSocket connectivity
   npm run test:websocket
   
   # Check connection logs
   grep "WebSocket" testing/results/logs/*.log
   ```

4. **Assessment Processing Timeouts**
   ```env
   # Increase timeout in .env
   ASSESSMENT_TIMEOUT=600000  # 10 minutes
   ```

5. **Rate Limiting Issues**
   ```env
   # Add delays between requests
   TEST_DELAY=1000
   ```

### Debug Mode Usage
```bash
# Enable comprehensive debugging
DEBUG_MODE=true LOG_LEVEL=debug SAVE_RESPONSES=true npm test

# Analyze saved responses
ls -la testing/results/responses/

# Review detailed logs
tail -f testing/results/logs/test-*.log
```

This testing suite provides comprehensive validation of the ATMA backend system, ensuring reliability, performance, and correctness across all user flows and system integrations.
