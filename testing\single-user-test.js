#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const WebSocketClient = require('./lib/websocket-client');
const ResponseValidator = require('./lib/validators');

class SingleUserE2ETest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    this.websocket = null;
    this.user = null;
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA E2E Test - Single User Flow');
    
    try {
      // Generate test user
      this.logger.step(1, 9, 'Generating test user');
      this.user = this.testData.generateTestUser(1);
      this.logger.info(`Generated test user: ${this.user.email}`);

      // Test 1: Register user
      this.logger.step(2, 9, 'Registering user');
      await this.testUserRegistration();

      // Test 2: Login user
      this.logger.step(3, 9, 'Logging in user');
      await this.testUserLogin();

      // Test 3: Connect WebSocket
      this.logger.step(4, 9, 'Connecting WebSocket');
      await this.testWebSocketConnection();

      // Test 4: Update profile
      this.logger.step(5, 9, 'Updating user profile');
      await this.testProfileUpdate();

      // Test 5: Submit assessment
      this.logger.step(6, 9, 'Submitting assessment');
      await this.testAssessmentSubmission();

      // Test 6: Wait for notification
      this.logger.step(7, 9, 'Waiting for WebSocket notification');
      await this.testWebSocketNotification();

      // Test 7: Get persona profile
      this.logger.step(8, 9, 'Retrieving persona profile');
      await this.testPersonaRetrieval();

      // Test 8: Test chatbot
      this.logger.step(9, 9, 'Testing chatbot functionality');
      await this.testChatbotFunctionality();

      // Summary
      this.printTestSummary();

    } catch (error) {
      this.logger.error('Test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  async testUserRegistration() {
    try {
      this.logger.info(`Registering user: ${this.user.email}`);
      
      const response = await this.api.register({
        email: this.user.email,
        password: this.user.password,
        username: this.user.username
      });

      // Validate response structure and content
      const validation = this.validator.validate(response, 'auth');
      if (!validation.isValid) {
        throw new Error(`Registration validation failed: ${validation.errors.join(', ')}`);
      }

      // Validate specific registration response fields according to API spec
      if (!response.data.user.token_balance || response.data.user.token_balance !== 5) {
        this.logger.warn('User token_balance not set to default value of 5');
      }

      if (response.data.user.user_type !== 'user') {
        throw new Error('User type should be "user" for registration');
      }

      // Store user data
      this.user.id = response.data.user.id;
      this.user.token = response.data.token;
      
      this.logger.success('User registered successfully');
      this.logger.info(`User ID: ${this.user.id}`);
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('User registration failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Registration: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testUserLogin() {
    try {
      this.logger.info(`Logging in user: ${this.user.email}`);
      
      const response = await this.api.login({
        email: this.user.email,
        password: this.user.password
      });

      // Validate response
      const validation = this.validator.validate(response, 'auth');
      if (!validation.isValid) {
        throw new Error(`Login validation failed: ${validation.errors.join(', ')}`);
      }

      // Update token (might be refreshed)
      this.user.token = response.data.token;
      
      this.logger.success('User logged in successfully');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('User login failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Login: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testWebSocketConnection() {
    try {
      this.logger.info('Connecting WebSocket');
      
      this.websocket = new WebSocketClient(null, this.logger);
      await this.websocket.connectAndAuthenticate(this.user.token);
      
      const state = this.websocket.getConnectionState();
      this.logger.success('WebSocket connected and authenticated');
      this.logger.info(`Connection state: ${JSON.stringify(state, null, 2)}`);
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('WebSocket connection failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`WebSocket: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testProfileUpdate() {
    try {
      this.logger.info('Updating user profile');
      
      // Set auth token
      this.api.setAuthToken(this.user.token);
      
      const response = await this.api.updateProfile(this.user.profileData);

      // Validate response
      const validation = this.validator.validate(response, 'auth');
      if (!validation.isValid) {
        throw new Error(`Profile update validation failed: ${validation.errors.join(', ')}`);
      }
      
      this.logger.success('User profile updated successfully');
      this.logger.info(`Updated username: ${this.user.profileData.username}`);
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Profile update failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Profile update: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testAssessmentSubmission() {
    try {
      this.logger.info('Submitting assessment');
      
      // Set auth token
      this.api.setAuthToken(this.user.token);

      // Generate idempotency key for assessment submission
      const idempotencyKey = `test-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

      const response = await this.api.submitAssessment(this.user.assessmentData, idempotencyKey);

      // Validate response
      const validation = this.validator.validate(response, 'assessment-submission');
      if (!validation.isValid) {
        throw new Error(`Assessment submission validation failed: ${validation.errors.join(', ')}`);
      }

      // Store job ID
      this.user.jobId = response.data.jobId;

      // Validate assessment submission response according to API spec
      if (response.data.status !== 'queued') {
        this.logger.warn(`Expected status 'queued', got '${response.data.status}'`);
      }

      if (!response.data.queuePosition || response.data.queuePosition < 1) {
        this.logger.warn('Queue position not provided or invalid');
      }

      this.logger.success('Assessment submitted successfully');
      this.logger.info(`Job ID: ${this.user.jobId}`);
      this.logger.info(`Status: ${response.data.status}`);
      this.logger.info(`Queue Position: ${response.data.queuePosition || 'N/A'}`);
      this.logger.info(`Estimated processing time: ${response.data.estimatedProcessingTime}`);
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Assessment submission failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Assessment: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testWebSocketNotification() {
    const timeout = parseInt(process.env.ASSESSMENT_TIMEOUT) || 300000;
    
    try {
      this.logger.info('Waiting for assessment completion notification...');
      this.logger.info(`Timeout: ${timeout / 1000} seconds`);
      
      const result = await this.websocket.waitForAssessmentCompletion(timeout);
      
      // Store result ID
      this.user.resultId = result.resultId;
      
      this.logger.success('Assessment completion notification received');
      this.logger.info(`Result ID: ${this.user.resultId}`);
      this.logger.info(`Job ID: ${result.jobId}`);
      this.logger.info(`Status: ${result.status}`);
      
      // Show all notifications received
      const notifications = this.websocket.getNotifications();
      this.logger.info(`Total notifications received: ${notifications.length}`);
      notifications.forEach((notif, index) => {
        this.logger.info(`Notification ${index + 1}: ${notif.type} at ${notif.timestamp.toISOString()}`);
      });
      
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('WebSocket notification failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Notification: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testPersonaRetrieval() {
    try {
      this.logger.info('Retrieving persona profile');
      
      // Set auth token
      this.api.setAuthToken(this.user.token);
      
      const response = await this.api.getResult(this.user.resultId);

      // Validate response
      const validation = this.validator.validate(response, 'assessment-result');
      if (!validation.isValid) {
        throw new Error(`Persona retrieval validation failed: ${validation.errors.join(', ')}`);
      }

      // Store persona data
      this.user.personaProfile = response.data.persona_profile;
      
      this.logger.success('Persona profile retrieved successfully');
      this.logger.info(`Archetype: ${this.user.personaProfile.archetype}`);
      this.logger.info(`Summary: ${this.user.personaProfile.shortSummary.substring(0, 100)}...`);
      this.logger.info(`Strengths: ${this.user.personaProfile.strengths.join(', ')}`);
      this.logger.info(`Career recommendations: ${this.user.personaProfile.careerRecommendation.length}`);
      
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Persona retrieval failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Persona: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testChatbotFunctionality() {
    try {
      this.logger.info('Testing chatbot functionality');
      
      // Set auth token
      this.api.setAuthToken(this.user.token);
      
      // Create conversation from assessment
      const convResponse = await this.api.createConversationFromAssessment({
        assessment_id: this.user.resultId,
        title: 'E2E Test Conversation',
        auto_start_message: true
      });

      // Validate conversation response
      const convValidation = this.validator.validate(convResponse, 'chatbot');
      if (!convValidation.isValid) {
        throw new Error(`Conversation creation validation failed: ${convValidation.errors.join(', ')}`);
      }

      this.user.conversationId = convResponse.data.conversation.id;
      this.logger.info(`Conversation created: ${this.user.conversationId}`);

      // Send test messages
      for (let i = 0; i < Math.min(3, this.user.chatMessages.length); i++) {
        const message = this.user.chatMessages[i];
        this.logger.info(`Sending message ${i + 1}: "${message.substring(0, 50)}..."`);
        
        const messageResponse = await this.api.sendMessage(this.user.conversationId, {
          content: message,
          content_type: "text"
        });

        // Validate message response
        const msgValidation = this.validator.validate(messageResponse, 'chatbot');
        if (!msgValidation.isValid) {
          throw new Error(`Message validation failed: ${msgValidation.errors.join(', ')}`);
        }

        this.logger.info(`Assistant response: "${messageResponse.data.assistant_message.content.substring(0, 100)}..."`);
        
        // Small delay between messages
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      this.logger.success('Chatbot test completed successfully');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Chatbot test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Chatbot: ${error.message}`);
      // Don't throw here, continue with cleanup
    }
    this.testResults.total++;
  }

  async cleanup() {
    this.logger.info('Performing cleanup...');
    
    try {
      // Disconnect WebSocket
      if (this.websocket) {
        this.websocket.disconnect();
        this.logger.info('WebSocket disconnected');
      }

      if (this.user && this.user.token) {
        // Set auth token
        this.api.setAuthToken(this.user.token);
        
        // Delete conversation if exists
        if (this.user.conversationId) {
          try {
            await this.api.deleteConversation(this.user.conversationId);
            this.logger.info('Conversation deleted');
          } catch (error) {
            this.logger.warn('Failed to delete conversation:', error.message);
          }
        }

        // Delete assessment result if exists
        if (this.user.resultId) {
          try {
            await this.api.deleteResult(this.user.resultId);
            this.logger.info('Assessment result deleted');
          } catch (error) {
            this.logger.warn('Failed to delete assessment result:', error.message);
          }
        }

        // Logout user
        try {
          await this.api.logout();
          this.logger.info('User logged out');
        } catch (error) {
          this.logger.warn('Failed to logout user:', error.message);
        }
      }

      // Clear auth token
      this.api.setAuthToken(null);
      
      this.logger.success('Cleanup completed');
      
    } catch (error) {
      this.logger.error('Cleanup failed:', error.message);
    }
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    // User summary
    if (this.user) {
      this.logger.info('\nTest User Summary:');
      this.logger.info(`Email: ${this.user.email}`);
      this.logger.info(`ID: ${this.user.id || 'N/A'}`);
      this.logger.info(`Job ID: ${this.user.jobId || 'N/A'}`);
      this.logger.info(`Result ID: ${this.user.resultId || 'N/A'}`);
      this.logger.info(`Archetype: ${this.user.personaProfile?.archetype || 'N/A'}`);
      this.logger.info(`Conversation ID: ${this.user.conversationId || 'N/A'}`);
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All tests passed! Single user E2E test completed successfully.');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} test(s) failed. Please check the errors above.`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const test = new SingleUserE2ETest();
  test.runTest().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = SingleUserE2ETest;
