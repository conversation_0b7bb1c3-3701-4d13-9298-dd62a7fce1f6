#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const APIClient = require('./lib/api-client');
const fs = require('fs');
const path = require('path');

class TestUserCleanup {
  constructor() {
    this.logger = new Logger();
    this.api = new APIClient(null, this.logger);
    this.testEmailDomain = process.env.TEST_EMAIL_DOMAIN || 'test.atma.local';
    
    this.cleanupResults = {
      usersFound: 0,
      conversationsDeleted: 0,
      resultsDeleted: 0,
      usersLoggedOut: 0,
      errors: []
    };
  }

  async runCleanup() {
    this.logger.header('ATMA Test User Cleanup Utility');
    
    try {
      // Method 1: Clean up from saved test data
      await this.cleanupFromSavedData();
      
      // Method 2: Clean up by email pattern (if admin access available)
      await this.cleanupByEmailPattern();
      
      // Method 3: Clean up old test files
      await this.cleanupTestFiles();
      
      this.printCleanupSummary();
      
    } catch (error) {
      this.logger.error('Cleanup failed:', error.message);
      this.cleanupResults.errors.push(error.message);
    }
  }

  async cleanupFromSavedData() {
    this.logger.step(1, 3, 'Cleaning up from saved test data');
    
    try {
      // Look for saved test user data in logs
      const logsDir = path.join(__dirname, 'results', 'logs');
      if (!fs.existsSync(logsDir)) {
        this.logger.info('No logs directory found, skipping saved data cleanup');
        return;
      }
      
      const logFiles = fs.readdirSync(logsDir).filter(f => f.endsWith('.log'));
      this.logger.info(`Found ${logFiles.length} log files to scan`);
      
      const testUsers = [];
      
      // Parse log files to extract test user information
      for (const logFile of logFiles) {
        try {
          const logPath = path.join(logsDir, logFile);
          const logContent = fs.readFileSync(logPath, 'utf8');
          const logLines = logContent.split('\n').filter(line => line.trim());
          
          for (const line of logLines) {
            try {
              const logEntry = JSON.parse(line);
              
              // Look for user registration or login data
              if (logEntry.data && logEntry.data.user && logEntry.data.token) {
                const user = {
                  id: logEntry.data.user.id,
                  email: logEntry.data.user.email,
                  token: logEntry.data.token
                };
                
                // Only process test users
                if (user.email.includes(this.testEmailDomain)) {
                  testUsers.push(user);
                }
              }
            } catch (parseError) {
              // Skip invalid JSON lines
            }
          }
        } catch (fileError) {
          this.logger.warn(`Failed to read log file ${logFile}:`, fileError.message);
        }
      }
      
      // Remove duplicates
      const uniqueUsers = testUsers.filter((user, index, self) => 
        index === self.findIndex(u => u.id === user.id)
      );
      
      this.logger.info(`Found ${uniqueUsers.length} unique test users in logs`);
      this.cleanupResults.usersFound += uniqueUsers.length;
      
      // Clean up each user
      for (const user of uniqueUsers) {
        await this.cleanupUser(user);
      }
      
    } catch (error) {
      this.logger.error('Failed to cleanup from saved data:', error.message);
      this.cleanupResults.errors.push(`Saved data cleanup: ${error.message}`);
    }
  }

  async cleanupByEmailPattern() {
    this.logger.step(2, 3, 'Cleaning up by email pattern (requires admin access)');
    
    try {
      // Note: This would require admin endpoints to list users by email pattern
      // Since the current API doesn't provide this functionality, we'll skip this step
      this.logger.info('Admin user listing not available in current API, skipping pattern cleanup');
      
    } catch (error) {
      this.logger.error('Failed to cleanup by email pattern:', error.message);
      this.cleanupResults.errors.push(`Pattern cleanup: ${error.message}`);
    }
  }

  async cleanupTestFiles() {
    this.logger.step(3, 3, 'Cleaning up old test files');
    
    try {
      const resultsDir = path.join(__dirname, 'results');
      if (!fs.existsSync(resultsDir)) {
        this.logger.info('No results directory found, skipping file cleanup');
        return;
      }
      
      // Clean up old log files (older than 7 days)
      const logsDir = path.join(resultsDir, 'logs');
      if (fs.existsSync(logsDir)) {
        const logFiles = fs.readdirSync(logsDir);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 7);
        
        let deletedLogs = 0;
        for (const logFile of logFiles) {
          const logPath = path.join(logsDir, logFile);
          const stats = fs.statSync(logPath);
          
          if (stats.mtime < cutoffDate) {
            fs.unlinkSync(logPath);
            deletedLogs++;
          }
        }
        
        this.logger.info(`Deleted ${deletedLogs} old log files`);
      }
      
      // Clean up old response files (older than 7 days)
      const responsesDir = path.join(resultsDir, 'responses');
      if (fs.existsSync(responsesDir)) {
        const responseFiles = fs.readdirSync(responsesDir);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 7);
        
        let deletedResponses = 0;
        for (const responseFile of responseFiles) {
          const responsePath = path.join(responsesDir, responseFile);
          const stats = fs.statSync(responsePath);
          
          if (stats.mtime < cutoffDate) {
            fs.unlinkSync(responsePath);
            deletedResponses++;
          }
        }
        
        this.logger.info(`Deleted ${deletedResponses} old response files`);
      }
      
    } catch (error) {
      this.logger.error('Failed to cleanup test files:', error.message);
      this.cleanupResults.errors.push(`File cleanup: ${error.message}`);
    }
  }

  async cleanupUser(user) {
    try {
      this.logger.info(`Cleaning up user: ${user.email}`);
      
      // Set auth token
      this.api.setAuthToken(user.token);
      
      // Get user's conversations
      try {
        const conversationsResponse = await this.api.getConversations({ limit: 100 });
        const conversations = conversationsResponse.data.conversations || [];
        
        for (const conversation of conversations) {
          try {
            await this.api.deleteConversation(conversation.id);
            this.cleanupResults.conversationsDeleted++;
            this.logger.info(`Deleted conversation: ${conversation.id}`);
          } catch (error) {
            this.logger.warn(`Failed to delete conversation ${conversation.id}:`, error.message);
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to get conversations for ${user.email}:`, error.message);
      }
      
      // Get user's assessment results
      try {
        const resultsResponse = await this.api.getResults({ limit: 100 });
        const results = resultsResponse.data.results || [];
        
        for (const result of results) {
          try {
            await this.api.deleteResult(result.id);
            this.cleanupResults.resultsDeleted++;
            this.logger.info(`Deleted result: ${result.id}`);
          } catch (error) {
            this.logger.warn(`Failed to delete result ${result.id}:`, error.message);
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to get results for ${user.email}:`, error.message);
      }
      
      // Logout user
      try {
        await this.api.logout();
        this.cleanupResults.usersLoggedOut++;
        this.logger.info(`Logged out user: ${user.email}`);
      } catch (error) {
        this.logger.warn(`Failed to logout ${user.email}:`, error.message);
      }
      
      // Clear auth token
      this.api.setAuthToken(null);
      
      this.logger.success(`Cleanup completed for user: ${user.email}`);
      
    } catch (error) {
      this.logger.error(`Failed to cleanup user ${user.email}:`, error.message);
      this.cleanupResults.errors.push(`User ${user.email}: ${error.message}`);
    }
  }

  async interactiveCleanup() {
    this.logger.header('ATMA Interactive Test User Cleanup');
    
    // This would implement an interactive cleanup where the user can:
    // 1. Enter specific user emails to clean up
    // 2. Enter specific user IDs to clean up
    // 3. Enter specific tokens to clean up
    
    this.logger.info('Interactive cleanup not implemented yet');
    this.logger.info('Use the automatic cleanup or provide user data manually');
  }

  async cleanupSpecificUser(email, token) {
    this.logger.info(`Cleaning up specific user: ${email}`);
    
    const user = { email, token };
    await this.cleanupUser(user);
  }

  async cleanupSpecificUsers(users) {
    this.logger.header(`ATMA Cleanup for ${users.length} Specific Users`);
    
    for (const user of users) {
      await this.cleanupUser(user);
    }
    
    this.printCleanupSummary();
  }

  printCleanupSummary() {
    this.logger.separator();
    this.logger.header('CLEANUP SUMMARY');
    
    const summary = {
      'Users Found': this.cleanupResults.usersFound,
      'Conversations Deleted': this.cleanupResults.conversationsDeleted,
      'Results Deleted': this.cleanupResults.resultsDeleted,
      'Users Logged Out': this.cleanupResults.usersLoggedOut,
      'Errors': this.cleanupResults.errors.length
    };
    
    this.logger.table(summary, 'Cleanup Results');
    
    if (this.cleanupResults.errors.length > 0) {
      this.logger.error('Errors encountered during cleanup:');
      this.cleanupResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    this.logger.separator();
    
    if (this.cleanupResults.errors.length === 0) {
      this.logger.success('🧹 Cleanup completed successfully!');
    } else {
      this.logger.warn(`⚠️ Cleanup completed with ${this.cleanupResults.errors.length} error(s).`);
    }
    
    this.logger.info('\nCleanup Notes:');
    this.logger.info('- User accounts cannot be deleted via API (requires admin/database access)');
    this.logger.info('- Only conversations and assessment results are cleaned up');
    this.logger.info('- Users are logged out to invalidate sessions');
    this.logger.info('- Old test files (>7 days) are automatically removed');
  }

  // Utility method to clean up from command line arguments
  static async cleanupFromArgs() {
    const args = process.argv.slice(2);
    const cleanup = new TestUserCleanup();
    
    if (args.length === 0) {
      // Run automatic cleanup
      await cleanup.runCleanup();
    } else if (args[0] === '--interactive') {
      // Run interactive cleanup
      await cleanup.interactiveCleanup();
    } else if (args[0] === '--user' && args.length >= 3) {
      // Clean up specific user: --user email token
      const email = args[1];
      const token = args[2];
      await cleanup.cleanupSpecificUser(email, token);
      cleanup.printCleanupSummary();
    } else {
      console.log('Usage:');
      console.log('  node cleanup-test-users.js                    # Automatic cleanup');
      console.log('  node cleanup-test-users.js --interactive      # Interactive cleanup');
      console.log('  node cleanup-test-users.js --user email token # Clean specific user');
    }
  }
}

// Run cleanup if this file is executed directly
if (require.main === module) {
  TestUserCleanup.cleanupFromArgs().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = TestUserCleanup;
