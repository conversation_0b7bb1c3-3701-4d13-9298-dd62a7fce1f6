const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

class Logger {
  constructor(options = {}) {
    this.logLevel = options.logLevel || process.env.LOG_LEVEL || 'info';
    this.debugMode = options.debugMode || process.env.DEBUG_MODE === 'true';
    this.saveToFile = options.saveToFile !== false;
    
    // Create logs directory if it doesn't exist
    if (this.saveToFile) {
      const logsDir = path.join(__dirname, '..', 'results', 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      this.logFile = path.join(logsDir, `test-${timestamp}.log`);
    }
  }

  _getTimestamp() {
    return new Date().toISOString();
  }

  _writeToFile(level, message, data = null) {
    if (!this.saveToFile) return;
    
    const logEntry = {
      timestamp: this._getTimestamp(),
      level,
      message,
      data
    };
    
    try {
      fs.appendFileSync(this.logFile, JSON.stringify(logEntry) + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  _shouldLog(level) {
    const levels = { error: 0, warn: 1, info: 2, debug: 3 };
    return levels[level] <= levels[this.logLevel];
  }

  error(message, data = null) {
    if (!this._shouldLog('error')) return;
    
    const timestamp = chalk.gray(`[${this._getTimestamp()}]`);
    const level = chalk.red.bold('[ERROR]');
    console.error(`${timestamp} ${level} ${chalk.red(message)}`);
    
    if (data) {
      console.error(chalk.red(JSON.stringify(data, null, 2)));
    }
    
    this._writeToFile('error', message, data);
  }

  warn(message, data = null) {
    if (!this._shouldLog('warn')) return;
    
    const timestamp = chalk.gray(`[${this._getTimestamp()}]`);
    const level = chalk.yellow.bold('[WARN]');
    console.warn(`${timestamp} ${level} ${chalk.yellow(message)}`);
    
    if (data) {
      console.warn(chalk.yellow(JSON.stringify(data, null, 2)));
    }
    
    this._writeToFile('warn', message, data);
  }

  info(message, data = null) {
    if (!this._shouldLog('info')) return;
    
    const timestamp = chalk.gray(`[${this._getTimestamp()}]`);
    const level = chalk.blue.bold('[INFO]');
    console.log(`${timestamp} ${level} ${chalk.blue(message)}`);
    
    if (data && this.debugMode) {
      console.log(chalk.blue(JSON.stringify(data, null, 2)));
    }
    
    this._writeToFile('info', message, data);
  }

  debug(message, data = null) {
    if (!this._shouldLog('debug') || !this.debugMode) return;
    
    const timestamp = chalk.gray(`[${this._getTimestamp()}]`);
    const level = chalk.magenta.bold('[DEBUG]');
    console.log(`${timestamp} ${level} ${chalk.magenta(message)}`);
    
    if (data) {
      console.log(chalk.magenta(JSON.stringify(data, null, 2)));
    }
    
    this._writeToFile('debug', message, data);
  }

  success(message, data = null) {
    const timestamp = chalk.gray(`[${this._getTimestamp()}]`);
    const level = chalk.green.bold('[SUCCESS]');
    console.log(`${timestamp} ${level} ${chalk.green(message)}`);
    
    if (data && this.debugMode) {
      console.log(chalk.green(JSON.stringify(data, null, 2)));
    }
    
    this._writeToFile('info', `SUCCESS: ${message}`, data);
  }

  step(stepNumber, totalSteps, message) {
    const timestamp = chalk.gray(`[${this._getTimestamp()}]`);
    const step = chalk.cyan.bold(`[STEP ${stepNumber}/${totalSteps}]`);
    console.log(`${timestamp} ${step} ${chalk.cyan(message)}`);
    
    this._writeToFile('info', `STEP ${stepNumber}/${totalSteps}: ${message}`);
  }

  separator() {
    const line = chalk.gray('='.repeat(80));
    console.log(line);
  }

  header(title) {
    this.separator();
    const timestamp = chalk.gray(`[${this._getTimestamp()}]`);
    const header = chalk.white.bold.bgBlue(` ${title} `);
    console.log(`${timestamp} ${header}`);
    this.separator();
    
    this._writeToFile('info', `HEADER: ${title}`);
  }

  table(data, title = null) {
    if (title) {
      console.log(chalk.cyan.bold(`\n${title}:`));
    }
    
    console.table(data);
    
    this._writeToFile('info', title ? `TABLE ${title}` : 'TABLE', data);
  }
}

module.exports = Logger;
