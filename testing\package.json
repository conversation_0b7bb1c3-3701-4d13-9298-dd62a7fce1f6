{"name": "atma-e2e-testing", "version": "1.0.0", "description": "End-to-End Testing Client for ATMA Backend Services", "main": "index.js", "scripts": {"test": "node test-runner.js", "test:all": "node run-all-tests.js", "test:compliance": "node run-all-tests.js", "test:headers": "node header-validation-test.js", "test:websocket-validation": "node websocket-notification-validation-test.js", "test:single": "node single-user-test.js", "test:dual": "node dual-user-test.js", "test:api": "node comprehensive-api-test.js", "test:websocket-comprehensive": "node websocket-comprehensive-test.js", "test:websocket": "node websocket-test.js", "test:chatbot": "node chatbot-test.js", "test:cleanup": "node cleanup-test-users.js", "setup": "node install.js", "postinstall": "node install.js"}, "dependencies": {"axios": "^1.6.0", "socket.io-client": "^4.7.2", "uuid": "^9.0.1", "chalk": "^4.1.2", "dotenv": "^16.3.1", "faker": "^6.6.6"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["e2e", "testing", "atma", "websocket", "api"], "author": "ATMA Development Team", "license": "MIT"}