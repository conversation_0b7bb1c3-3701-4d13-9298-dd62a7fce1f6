#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const WebSocketClient = require('./lib/websocket-client');
const ResponseValidator = require('./lib/validators');

class WebSocketOnlyTest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    this.websocket = null;
    this.user = null;
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA WebSocket Only Test');
    
    try {
      // Setup: Create user and get token
      await this.setupTestUser();

      // Test 1: Basic WebSocket connection
      this.logger.step(1, 6, 'Testing basic WebSocket connection');
      await this.testBasicConnection();

      // Test 2: Authentication
      this.logger.step(2, 6, 'Testing WebSocket authentication');
      await this.testAuthentication();

      // Test 3: Connection state management
      this.logger.step(3, 6, 'Testing connection state management');
      await this.testConnectionStates();

      // Test 4: Submit assessment and wait for notifications
      this.logger.step(4, 6, 'Testing real-time notifications');
      await this.testRealTimeNotifications();

      // Test 5: Notification validation
      this.logger.step(5, 6, 'Testing notification validation');
      await this.testNotificationValidation();

      // Test 6: Reconnection handling
      this.logger.step(6, 6, 'Testing reconnection handling');
      await this.testReconnection();

      this.printTestSummary();

    } catch (error) {
      this.logger.error('WebSocket test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  async setupTestUser() {
    this.logger.info('Setting up test user...');
    
    this.user = this.testData.generateTestUser(1);
    
    // Register user
    const registerResponse = await this.api.register({
      email: this.user.email,
      password: this.user.password,
      username: this.user.username
    });
    
    this.user.id = registerResponse.data.user.id;
    this.user.token = registerResponse.data.token;
    
    this.logger.success(`Test user created: ${this.user.email}`);
  }

  async testBasicConnection() {
    try {
      this.logger.info('Testing basic WebSocket connection...');
      
      this.websocket = new WebSocketClient(null, this.logger);
      
      // Test connection without authentication
      await this.websocket.connect();
      
      const state = this.websocket.getConnectionState();
      if (!state.isConnected) {
        throw new Error('WebSocket connection failed');
      }
      
      if (state.state !== 'connected') {
        throw new Error(`Expected state 'connected', got '${state.state}'`);
      }
      
      this.logger.success('Basic WebSocket connection successful');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Basic connection test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Basic connection: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testAuthentication() {
    try {
      this.logger.info('Testing WebSocket authentication...');
      
      // Test authentication with valid token
      await this.websocket.authenticate(this.user.token);
      
      const state = this.websocket.getConnectionState();
      if (!state.isAuthenticated) {
        throw new Error('WebSocket authentication failed');
      }
      
      if (state.state !== 'authenticated') {
        throw new Error(`Expected state 'authenticated', got '${state.state}'`);
      }
      
      if (state.userId !== this.user.id) {
        throw new Error(`User ID mismatch: expected ${this.user.id}, got ${state.userId}`);
      }
      
      this.logger.success('WebSocket authentication successful');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Authentication test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Authentication: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testConnectionStates() {
    try {
      this.logger.info('Testing connection state management...');
      
      // Test current state
      let state = this.websocket.getConnectionState();
      this.logger.info(`Current state: ${JSON.stringify(state, null, 2)}`);
      
      if (state.state !== 'authenticated') {
        throw new Error(`Expected authenticated state, got ${state.state}`);
      }
      
      // Test notification count
      if (typeof state.notificationCount !== 'number') {
        throw new Error('Notification count should be a number');
      }
      
      // Test state properties
      const requiredProperties = ['state', 'isConnected', 'isAuthenticated', 'userId', 'notificationCount'];
      for (const prop of requiredProperties) {
        if (!state.hasOwnProperty(prop)) {
          throw new Error(`Missing state property: ${prop}`);
        }
      }
      
      this.logger.success('Connection state management test passed');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Connection state test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Connection state: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testRealTimeNotifications() {
    try {
      this.logger.info('Testing real-time notifications...');
      
      // Set up notification listeners
      const notifications = [];
      
      this.websocket.on('analysis-started', (data) => {
        notifications.push({ type: 'analysis-started', data });
      });
      
      this.websocket.on('analysis-complete', (data) => {
        notifications.push({ type: 'analysis-complete', data });
      });
      
      this.websocket.on('analysis-failed', (data) => {
        notifications.push({ type: 'analysis-failed', data });
      });
      
      // Submit assessment to trigger notifications
      this.api.setAuthToken(this.user.token);
      const assessmentResponse = await this.api.submitAssessment(this.user.assessmentData);
      this.user.jobId = assessmentResponse.data.jobId;
      
      this.logger.info(`Assessment submitted with job ID: ${this.user.jobId}`);
      
      // Wait for notifications
      const timeout = parseInt(process.env.ASSESSMENT_TIMEOUT) || 300000;
      const result = await this.websocket.waitForAssessmentCompletion(timeout);
      
      this.user.resultId = result.resultId;
      
      // Verify notifications were received
      const allNotifications = this.websocket.getNotifications();
      if (allNotifications.length === 0) {
        throw new Error('No notifications received');
      }
      
      // Check for analysis-started notification
      const startedNotifications = allNotifications.filter(n => n.type === 'analysis-started');
      if (startedNotifications.length === 0) {
        throw new Error('No analysis-started notification received');
      }
      
      // Check for analysis-complete notification
      const completeNotifications = allNotifications.filter(n => n.type === 'analysis-complete');
      if (completeNotifications.length === 0) {
        throw new Error('No analysis-complete notification received');
      }
      
      this.logger.success(`Real-time notifications test passed. Received ${allNotifications.length} notifications`);
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Real-time notifications test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Real-time notifications: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testNotificationValidation() {
    try {
      this.logger.info('Testing notification validation...');
      
      const notifications = this.websocket.getNotifications();
      
      for (const notification of notifications) {
        // Validate notification structure
        const validation = this.validator.validateWebSocketNotification(notification.data, notification.type);
        
        if (!validation.isValid) {
          throw new Error(`Notification validation failed for ${notification.type}: ${validation.errors.join(', ')}`);
        }
        
        // Additional validation using websocket client method
        const isValid = this.websocket.validateNotificationStructure(notification.data, notification.type);
        if (!isValid) {
          throw new Error(`WebSocket client validation failed for ${notification.type}`);
        }
      }
      
      this.logger.success(`Notification validation passed for ${notifications.length} notifications`);
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Notification validation test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Notification validation: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async testReconnection() {
    try {
      this.logger.info('Testing reconnection handling...');
      
      // Disconnect WebSocket
      this.websocket.disconnect();
      
      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reconnect and authenticate
      await this.websocket.connect();
      await this.websocket.authenticate(this.user.token);
      
      const state = this.websocket.getConnectionState();
      if (!state.isAuthenticated) {
        throw new Error('Reconnection and authentication failed');
      }
      
      // Clear previous notifications
      this.websocket.clearNotifications();
      
      // Test that we can still receive notifications after reconnection
      // We'll just verify the connection is working by checking the state
      if (state.state !== 'authenticated') {
        throw new Error(`Expected authenticated state after reconnection, got ${state.state}`);
      }
      
      this.logger.success('Reconnection handling test passed');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Reconnection test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Reconnection: ${error.message}`);
      throw error;
    }
    this.testResults.total++;
  }

  async cleanup() {
    this.logger.info('Performing cleanup...');
    
    try {
      // Disconnect WebSocket
      if (this.websocket) {
        this.websocket.disconnect();
        this.logger.info('WebSocket disconnected');
      }

      if (this.user && this.user.token) {
        // Set auth token
        this.api.setAuthToken(this.user.token);
        
        // Delete assessment result if exists
        if (this.user.resultId) {
          try {
            await this.api.deleteResult(this.user.resultId);
            this.logger.info('Assessment result deleted');
          } catch (error) {
            this.logger.warn('Failed to delete assessment result:', error.message);
          }
        }

        // Logout user
        try {
          await this.api.logout();
          this.logger.info('User logged out');
        } catch (error) {
          this.logger.warn('Failed to logout user:', error.message);
        }
      }

      // Clear auth token
      this.api.setAuthToken(null);
      
      this.logger.success('Cleanup completed');
      
    } catch (error) {
      this.logger.error('Cleanup failed:', error.message);
    }
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('WEBSOCKET TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    // WebSocket summary
    if (this.websocket) {
      const notifications = this.websocket.getNotifications();
      this.logger.info('\nWebSocket Summary:');
      this.logger.info(`Total notifications received: ${notifications.length}`);
      
      const notificationTypes = {};
      notifications.forEach(n => {
        notificationTypes[n.type] = (notificationTypes[n.type] || 0) + 1;
      });
      
      Object.entries(notificationTypes).forEach(([type, count]) => {
        this.logger.info(`  ${type}: ${count}`);
      });
    }

    // User summary
    if (this.user) {
      this.logger.info('\nTest User Summary:');
      this.logger.info(`Email: ${this.user.email}`);
      this.logger.info(`ID: ${this.user.id || 'N/A'}`);
      this.logger.info(`Job ID: ${this.user.jobId || 'N/A'}`);
      this.logger.info(`Result ID: ${this.user.resultId || 'N/A'}`);
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All WebSocket tests passed!');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} WebSocket test(s) failed.`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const test = new WebSocketOnlyTest();
  test.runTest().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = WebSocketOnlyTest;
