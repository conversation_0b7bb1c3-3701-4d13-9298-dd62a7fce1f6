#!/usr/bin/env node

require('dotenv').config();
const E2ETestRunner = require('./test-runner');

class DualUserE2ETest extends E2ETestRunner {
  constructor() {
    super();
    this.logger.info('Initializing Dual User E2E Test');
  }

  async runDualUserTest() {
    this.logger.header('ATMA E2E Test - Dual User Parallel Flow');
    
    try {
      // Generate test users
      this.logger.step(1, 8, 'Generating test users');
      this.users = this.testData.generateTestUsers(2);
      this.logger.info(`Generated ${this.users.length} test users`);
      this.users.forEach((user, index) => {
        this.logger.info(`User ${index + 1}: ${user.email}`);
      });

      // Test 1: Register users in parallel
      this.logger.step(2, 8, 'Registering users in parallel');
      await this.testParallelUserRegistration();

      // Test 2: Login users in parallel
      this.logger.step(3, 8, 'Logging in users in parallel');
      await this.testParallelUserLogin();

      // Test 3: Connect WebSockets in parallel
      this.logger.step(4, 8, 'Connecting WebSockets in parallel');
      await this.testParallelWebSocketConnection();

      // Test 4: Update profiles in parallel
      this.logger.step(5, 8, 'Updating user profiles in parallel');
      await this.testParallelProfileUpdate();

      // Test 5: Submit assessments in parallel
      this.logger.step(6, 8, 'Submitting assessments in parallel');
      await this.testParallelAssessmentSubmission();

      // Test 6: Wait for notifications in parallel
      this.logger.step(7, 8, 'Waiting for WebSocket notifications in parallel');
      await this.testParallelWebSocketNotifications();

      // Test 7: Test chatbot and cleanup in parallel
      this.logger.step(8, 8, 'Testing chatbot and cleanup in parallel');
      await this.testParallelChatbotAndCleanup();

      // Summary
      this.printTestSummary();

    } catch (error) {
      this.logger.error('Dual user test suite failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
      await this.emergencyCleanup();
    }
  }

  async testParallelUserRegistration() {
    const registrationPromises = this.users.map(async (user, index) => {
      try {
        this.logger.info(`Registering user ${index + 1}: ${user.email}`);
        
        const response = await this.api.register({
          email: user.email,
          password: user.password,
          username: user.username
        });

        // Validate response
        const validation = this.validator.validate(response, 'auth');
        if (!validation.isValid) {
          throw new Error(`Registration validation failed: ${validation.errors.join(', ')}`);
        }

        // Store user data
        user.id = response.data.user.id;
        user.token = response.data.token;
        
        this.logger.success(`User ${index + 1} registered successfully`);
        return { success: true, userIndex: index + 1 };
        
      } catch (error) {
        this.logger.error(`User ${index + 1} registration failed:`, error.message);
        this.testResults.errors.push(`User ${index + 1} registration: ${error.message}`);
        return { success: false, userIndex: index + 1, error: error.message };
      }
    });

    const results = await Promise.allSettled(registrationPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
      }
      this.testResults.total++;
    });

    // Check if any registration failed
    const failures = results.filter(r => r.status === 'rejected' || !r.value.success);
    if (failures.length > 0) {
      throw new Error(`${failures.length} user registration(s) failed`);
    }
  }

  async testParallelUserLogin() {
    const loginPromises = this.users.map(async (user, index) => {
      try {
        this.logger.info(`Logging in user ${index + 1}: ${user.email}`);
        
        const response = await this.api.login({
          email: user.email,
          password: user.password
        });

        // Validate response
        const validation = this.validator.validate(response, 'auth');
        if (!validation.isValid) {
          throw new Error(`Login validation failed: ${validation.errors.join(', ')}`);
        }

        // Update token (might be refreshed)
        user.token = response.data.token;
        
        this.logger.success(`User ${index + 1} logged in successfully`);
        return { success: true, userIndex: index + 1 };
        
      } catch (error) {
        this.logger.error(`User ${index + 1} login failed:`, error.message);
        this.testResults.errors.push(`User ${index + 1} login: ${error.message}`);
        return { success: false, userIndex: index + 1, error: error.message };
      }
    });

    const results = await Promise.allSettled(loginPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
      }
      this.testResults.total++;
    });

    // Check if any login failed
    const failures = results.filter(r => r.status === 'rejected' || !r.value.success);
    if (failures.length > 0) {
      throw new Error(`${failures.length} user login(s) failed`);
    }
  }

  async testParallelWebSocketConnection() {
    const wsPromises = this.users.map(async (user, index) => {
      try {
        this.logger.info(`Connecting WebSocket for user ${index + 1}`);
        
        const ws = new (require('./lib/websocket-client'))(null, this.logger);
        await ws.connectAndAuthenticate(user.token);
        
        user.websocket = ws;
        this.websockets.push(ws);
        
        this.logger.success(`User ${index + 1} WebSocket connected and authenticated`);
        return { success: true, userIndex: index + 1 };
        
      } catch (error) {
        this.logger.error(`User ${index + 1} WebSocket connection failed:`, error.message);
        this.testResults.errors.push(`User ${index + 1} WebSocket: ${error.message}`);
        return { success: false, userIndex: index + 1, error: error.message };
      }
    });

    const results = await Promise.allSettled(wsPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
      }
      this.testResults.total++;
    });

    // Check if any WebSocket connection failed
    const failures = results.filter(r => r.status === 'rejected' || !r.value.success);
    if (failures.length > 0) {
      throw new Error(`${failures.length} WebSocket connection(s) failed`);
    }
  }

  async testParallelProfileUpdate() {
    const profilePromises = this.users.map(async (user, index) => {
      try {
        this.logger.info(`Updating profile for user ${index + 1}`);
        
        // Create separate API client for each user to avoid token conflicts
        const userApi = new (require('./lib/api-client'))(null, this.logger);
        userApi.setAuthToken(user.token);
        
        const response = await userApi.updateProfile(user.profileData);

        // Validate response
        const validation = this.validator.validate(response, 'auth');
        if (!validation.isValid) {
          throw new Error(`Profile update validation failed: ${validation.errors.join(', ')}`);
        }
        
        this.logger.success(`User ${index + 1} profile updated successfully`);
        return { success: true, userIndex: index + 1 };
        
      } catch (error) {
        this.logger.error(`User ${index + 1} profile update failed:`, error.message);
        this.testResults.errors.push(`User ${index + 1} profile update: ${error.message}`);
        return { success: false, userIndex: index + 1, error: error.message };
      }
    });

    const results = await Promise.allSettled(profilePromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
      }
      this.testResults.total++;
    });

    // Check if any profile update failed
    const failures = results.filter(r => r.status === 'rejected' || !r.value.success);
    if (failures.length > 0) {
      throw new Error(`${failures.length} profile update(s) failed`);
    }
  }

  async testParallelAssessmentSubmission() {
    const assessmentPromises = this.users.map(async (user, index) => {
      try {
        this.logger.info(`Submitting assessment for user ${index + 1}`);
        
        // Create separate API client for each user to avoid token conflicts
        const userApi = new (require('./lib/api-client'))(null, this.logger);
        userApi.setAuthToken(user.token);
        
        const response = await userApi.submitAssessment(user.assessmentData);

        // Validate response
        const validation = this.validator.validate(response, 'assessment-submission');
        if (!validation.isValid) {
          throw new Error(`Assessment submission validation failed: ${validation.errors.join(', ')}`);
        }

        // Store job ID
        user.jobId = response.data.jobId;
        
        this.logger.success(`User ${index + 1} assessment submitted successfully. Job ID: ${user.jobId}`);
        return { success: true, userIndex: index + 1 };
        
      } catch (error) {
        this.logger.error(`User ${index + 1} assessment submission failed:`, error.message);
        this.testResults.errors.push(`User ${index + 1} assessment: ${error.message}`);
        return { success: false, userIndex: index + 1, error: error.message };
      }
    });

    const results = await Promise.allSettled(assessmentPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
      }
      this.testResults.total++;
    });

    // Check if any assessment submission failed
    const failures = results.filter(r => r.status === 'rejected' || !r.value.success);
    if (failures.length > 0) {
      throw new Error(`${failures.length} assessment submission(s) failed`);
    }
  }

  async testParallelWebSocketNotifications() {
    const timeout = parseInt(process.env.ASSESSMENT_TIMEOUT) || 300000;
    
    const notificationPromises = this.users.map(async (user, index) => {
      try {
        this.logger.info(`Waiting for assessment completion for user ${index + 1}`);
        
        const result = await user.websocket.waitForAssessmentCompletion(timeout);
        
        // Store result ID
        user.resultId = result.resultId;
        
        this.logger.success(`User ${index + 1} received assessment completion notification. Result ID: ${user.resultId}`);
        return { success: true, userIndex: index + 1 };
        
      } catch (error) {
        this.logger.error(`User ${index + 1} WebSocket notification failed:`, error.message);
        this.testResults.errors.push(`User ${index + 1} notification: ${error.message}`);
        return { success: false, userIndex: index + 1, error: error.message };
      }
    });

    const results = await Promise.allSettled(notificationPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.testResults.passed++;
      } else {
        this.testResults.failed++;
      }
      this.testResults.total++;
    });

    // Check if any notification failed
    const failures = results.filter(r => r.status === 'rejected' || !r.value.success);
    if (failures.length > 0) {
      throw new Error(`${failures.length} WebSocket notification(s) failed`);
    }
  }

  async testParallelChatbotAndCleanup() {
    const chatbotPromises = this.users.map(async (user, index) => {
      try {
        this.logger.info(`Testing chatbot and cleanup for user ${index + 1}`);
        
        // Create separate API client for each user
        const userApi = new (require('./lib/api-client'))(null, this.logger);
        userApi.setAuthToken(user.token);
        
        // Get persona profile first
        const resultResponse = await userApi.getResult(user.resultId);
        user.personaProfile = resultResponse.data.persona_profile;
        this.logger.info(`User ${index + 1} persona: ${user.personaProfile.archetype}`);
        
        // Test chatbot
        const convResponse = await userApi.createConversationFromAssessment({
          assessment_id: user.resultId,
          title: `E2E Test Chat - User ${index + 1}`,
          auto_start_message: true
        });

        user.conversationId = convResponse.data.conversation.id;

        // Send a test message
        await userApi.sendMessage(user.conversationId, {
          content: user.chatMessages[0],
          content_type: "text"
        });

        this.logger.success(`User ${index + 1} chatbot test completed`);
        
        // Cleanup
        await userApi.deleteConversation(user.conversationId);
        await userApi.deleteResult(user.resultId);
        user.websocket.disconnect();
        
        this.logger.success(`User ${index + 1} cleanup completed`);
        return { success: true, userIndex: index + 1 };
        
      } catch (error) {
        this.logger.error(`User ${index + 1} chatbot/cleanup failed:`, error.message);
        this.testResults.errors.push(`User ${index + 1} chatbot/cleanup: ${error.message}`);
        return { success: false, userIndex: index + 1, error: error.message };
      }
    });

    const results = await Promise.allSettled(chatbotPromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        this.testResults.passed += 2; // chatbot + cleanup
      } else {
        this.testResults.failed += 2;
      }
      this.testResults.total += 2;
    });
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('DUAL USER TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`,
      'Parallel Execution': 'Yes'
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    // User summary
    if (this.users.length > 0) {
      this.logger.info('\nTest Users Summary:');
      this.users.forEach((user, index) => {
        this.logger.info(`User ${index + 1}:`);
        this.logger.info(`  Email: ${user.email}`);
        this.logger.info(`  ID: ${user.id || 'N/A'}`);
        this.logger.info(`  Job ID: ${user.jobId || 'N/A'}`);
        this.logger.info(`  Result ID: ${user.resultId || 'N/A'}`);
        this.logger.info(`  Archetype: ${user.personaProfile?.archetype || 'N/A'}`);
        this.logger.info(`  Conversation ID: ${user.conversationId || 'N/A'}`);
      });
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All parallel tests passed! Dual user E2E test completed successfully.');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} test(s) failed. Please check the errors above.`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const test = new DualUserE2ETest();
  test.runDualUserTest().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = DualUserE2ETest;
