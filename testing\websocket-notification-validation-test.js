#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const WebSocketClient = require('./lib/websocket-client');
const ResponseValidator = require('./lib/validators');

class WebSocketNotificationValidationTest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    this.websocket = null;
    this.user = null;
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA WebSocket Notification Validation Test');
    
    try {
      // Setup test user and WebSocket connection
      await this.setupTestUser();
      await this.setupWebSocketConnection();

      // Test 1: Notification Structure Validation
      this.logger.step(1, 4, 'Testing notification structure validation');
      await this.testNotificationStructure();

      // Test 2: Metadata Validation
      this.logger.step(2, 4, 'Testing notification metadata validation');
      await this.testNotificationMetadata();

      // Test 3: Timestamp Validation
      this.logger.step(3, 4, 'Testing notification timestamp validation');
      await this.testTimestampValidation();

      // Test 4: Complete Assessment Flow Validation
      this.logger.step(4, 4, 'Testing complete assessment flow notification validation');
      await this.testCompleteAssessmentFlow();

      this.printTestSummary();

    } catch (error) {
      this.logger.error('WebSocket notification validation test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  async setupTestUser() {
    this.logger.info('Setting up test user...');
    
    this.user = this.testData.generateTestUser(1);
    
    // Register user
    const registerResponse = await this.api.register({
      email: this.user.email,
      password: this.user.password,
      username: this.user.username
    });
    
    this.user.id = registerResponse.data.user.id;
    this.user.token = registerResponse.data.token;
    this.api.setAuthToken(this.user.token);
    
    this.logger.success(`Test user created: ${this.user.email}`);
  }

  async setupWebSocketConnection() {
    this.logger.info('Setting up WebSocket connection...');
    
    this.websocket = new WebSocketClient(null, this.logger);
    await this.websocket.connectAndAuthenticate(this.user.token);
    
    this.logger.success('WebSocket connection established and authenticated');
  }

  async testNotificationStructure() {
    try {
      this.logger.info('Testing notification structure validation...');
      
      // Submit assessment to trigger notifications
      const assessmentData = this.testData.generateAssessmentData();
      const submitResponse = await this.api.submitAssessment(assessmentData);
      
      this.logger.info(`Assessment submitted with jobId: ${submitResponse.data.jobId}`);
      
      // Wait for analysis-started notification
      const startedNotification = await this.websocket.waitForNotification('analysis-started', 30000);
      
      // Validate analysis-started notification structure
      const startedValidation = this.validator.validateWebSocketNotification(startedNotification, 'analysis-started');
      if (!startedValidation.isValid) {
        throw new Error(`Analysis-started notification validation failed: ${startedValidation.errors.join(', ')}`);
      }
      
      this.logger.success('Analysis-started notification structure validated successfully');
      
      // Wait for completion notification (either success or failure)
      const completionResult = await this.websocket.waitForAnyNotification(['analysis-complete', 'analysis-failed'], 300000);
      
      // Validate completion notification structure
      const completionValidation = this.validator.validateWebSocketNotification(completionResult.data, completionResult.type);
      if (!completionValidation.isValid) {
        throw new Error(`${completionResult.type} notification validation failed: ${completionValidation.errors.join(', ')}`);
      }
      
      this.logger.success(`${completionResult.type} notification structure validated successfully`);

      this.testResults.passed++;

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Notification structure test failed: ${error.message}`);
      this.logger.error('Notification structure test failed:', error.message);
    }
  }

  async testNotificationMetadata() {
    try {
      this.logger.info('Testing notification metadata validation...');
      
      // Get all notifications received so far
      const notifications = this.websocket.getNotifications();
      
      if (notifications.length === 0) {
        throw new Error('No notifications received to validate metadata');
      }
      
      for (const notification of notifications) {
        const { type, data } = notification;
        
        // Validate metadata according to WebSocket manual spec
        if (!data.metadata) {
          throw new Error(`${type} notification missing metadata`);
        }
        
        if (!data.metadata.assessmentName) {
          throw new Error(`${type} notification metadata missing assessmentName`);
        }
        
        // Type-specific metadata validation
        switch (type) {
          case 'analysis-started':
            if (!data.metadata.estimatedProcessingTime) {
              throw new Error('Analysis-started notification metadata missing estimatedProcessingTime');
            }
            break;
          case 'analysis-complete':
            if (!data.metadata.processingTime) {
              throw new Error('Analysis-complete notification metadata missing processingTime');
            }
            break;
          case 'analysis-failed':
            if (!data.metadata.errorType) {
              throw new Error('Analysis-failed notification metadata missing errorType');
            }
            break;
        }
        
        this.logger.success(`${type} notification metadata validated successfully`);
      }

      this.testResults.passed++;

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Notification metadata test failed: ${error.message}`);
      this.logger.error('Notification metadata test failed:', error.message);
    }
  }

  async testTimestampValidation() {
    try {
      this.logger.info('Testing notification timestamp validation...');
      
      const notifications = this.websocket.getNotifications();
      
      if (notifications.length === 0) {
        throw new Error('No notifications received to validate timestamps');
      }
      
      for (const notification of notifications) {
        const { type, data } = notification;
        
        // Validate timestamp format
        if (!data.timestamp) {
          throw new Error(`${type} notification missing timestamp`);
        }
        
        const timestamp = new Date(data.timestamp);
        if (isNaN(timestamp.getTime())) {
          throw new Error(`${type} notification has invalid timestamp format: ${data.timestamp}`);
        }
        
        // Validate timestamp is recent (within last hour)
        const now = new Date();
        const timeDiff = Math.abs(now - timestamp);
        const oneHour = 60 * 60 * 1000;
        
        if (timeDiff > oneHour) {
          this.logger.warn(`${type} notification timestamp seems old: ${data.timestamp}`);
        }
        
        this.logger.success(`${type} notification timestamp validated successfully`);
      }

      this.testResults.passed++;

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Timestamp validation test failed: ${error.message}`);
      this.logger.error('Timestamp validation test failed:', error.message);
    }
  }

  async testCompleteAssessmentFlow() {
    try {
      this.logger.info('Testing complete assessment flow notification validation...');
      
      // Clear previous notifications
      this.websocket.clearNotifications();
      
      // Submit new assessment
      const assessmentData = this.testData.generateAssessmentData();
      const submitResponse = await this.api.submitAssessment(assessmentData);
      
      this.logger.info(`New assessment submitted with jobId: ${submitResponse.data.jobId}`);
      
      // Wait for complete assessment flow
      const completionData = await this.websocket.waitForAssessmentCompletion(300000);
      
      // Get validation statistics
      const validationStats = this.websocket.getNotificationValidationStats();
      
      this.logger.info('Notification validation statistics:', validationStats);
      
      if (validationStats.validationRate < 100) {
        throw new Error(`Notification validation rate is ${validationStats.validationRate}%, expected 100%`);
      }
      
      // Validate that we received the expected sequence
      const notifications = this.websocket.getNotifications();
      const types = notifications.map(n => n.type);
      
      if (!types.includes('analysis-started')) {
        throw new Error('Missing analysis-started notification in flow');
      }
      
      if (!types.includes('analysis-complete') && !types.includes('analysis-failed')) {
        throw new Error('Missing completion notification in flow');
      }
      
      this.logger.success('Complete assessment flow validation passed');
      this.testResults.passed++;

    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push(`Complete flow test failed: ${error.message}`);
      this.logger.error('Complete flow test failed:', error.message);
    }
  }

  printTestSummary() {
    this.testResults.total = this.testResults.passed + this.testResults.failed;
    
    this.logger.header('WebSocket Notification Validation Test Summary');
    this.logger.info(`Total Tests: ${this.testResults.total}`);
    this.logger.success(`Passed: ${this.testResults.passed}`);
    
    if (this.testResults.failed > 0) {
      this.logger.error(`Failed: ${this.testResults.failed}`);
      this.logger.error('Errors:');
      this.testResults.errors.forEach(error => {
        this.logger.error(`  - ${error}`);
      });
    }
    
    // Print notification validation statistics
    if (this.websocket) {
      const validationStats = this.websocket.getNotificationValidationStats();
      this.logger.info('Final Notification Validation Statistics:', validationStats);
    }
    
    const successRate = this.testResults.total > 0 ? 
      ((this.testResults.passed / this.testResults.total) * 100).toFixed(1) : 0;
    this.logger.info(`Success Rate: ${successRate}%`);
  }

  async cleanup() {
    try {
      if (this.websocket) {
        this.logger.info('Disconnecting WebSocket...');
        this.websocket.disconnect();
      }
      
      if (this.user && this.user.id) {
        this.logger.info('Cleaning up test user...');
        await this.api.logout();
      }
    } catch (error) {
      this.logger.warn('Cleanup failed:', error.message);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  const test = new WebSocketNotificationValidationTest();
  test.runTest()
    .then(() => {
      process.exit(test.testResults.failed > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = WebSocketNotificationValidationTest;
