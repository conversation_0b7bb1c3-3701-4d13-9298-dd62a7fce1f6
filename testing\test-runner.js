#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const WebSocketClient = require('./lib/websocket-client');
const ResponseValidator = require('./lib/validators');

// Import test modules
const ComprehensiveAPITest = require('./comprehensive-api-test');
const WebSocketComprehensiveTest = require('./websocket-comprehensive-test');
const ErrorScenariosTest = require('./error-scenarios-test');
const HeaderValidationTest = require('./header-validation-test');
const WebSocketNotificationValidationTest = require('./websocket-notification-validation-test');

class E2ETestRunner {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
    
    this.users = [];
    this.websockets = [];
  }

  async runTests() {
    this.logger.header('ATMA E2E Test Suite - Dual User Flow');
    
    try {
      // Generate test users
      this.logger.step(1, 10, 'Generating test users');
      this.users = this.testData.generateTestUsers(2);
      this.logger.info(`Generated ${this.users.length} test users`);
      this.users.forEach((user, index) => {
        this.logger.info(`User ${index + 1}: ${user.email}`);
      });

      // Test 1: Register users
      this.logger.step(2, 10, 'Registering users');
      await this.testUserRegistration();

      // Test 2: Login users
      this.logger.step(3, 10, 'Logging in users');
      await this.testUserLogin();

      // Test 3: Connect WebSockets
      this.logger.step(4, 10, 'Connecting WebSockets');
      await this.testWebSocketConnection();

      // Test 4: Update profiles
      this.logger.step(5, 10, 'Updating user profiles');
      await this.testProfileUpdate();

      // Test 5: Submit assessments
      this.logger.step(6, 10, 'Submitting assessments');
      await this.testAssessmentSubmission();

      // Test 6: Wait for notifications
      this.logger.step(7, 10, 'Waiting for WebSocket notifications');
      await this.testWebSocketNotifications();

      // Test 7: Get persona profiles
      this.logger.step(8, 10, 'Retrieving persona profiles');
      await this.testPersonaRetrieval();

      // Test 8: Test chatbot
      this.logger.step(9, 10, 'Testing chatbot functionality');
      await this.testChatbotFunctionality();

      // Test 9: Cleanup
      this.logger.step(10, 10, 'Cleaning up test accounts');
      await this.testCleanup();

      // Summary
      this.printTestSummary();

    } catch (error) {
      this.logger.error('Test suite failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
      await this.emergencyCleanup();
    }
  }

  async testUserRegistration() {
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Registering user ${i + 1}: ${user.email}`);
        
        const response = await this.api.register({
          email: user.email,
          password: user.password,
          username: user.username
        });

        // Validate response
        const validation = this.validator.validate(response, 'auth');
        if (!validation.isValid) {
          throw new Error(`Registration validation failed: ${validation.errors.join(', ')}`);
        }

        // Store user data
        user.id = response.data.user.id;
        user.token = response.data.token;
        
        this.logger.success(`User ${i + 1} registered successfully`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} registration failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} registration: ${error.message}`);
        throw error;
      }
    }
    this.testResults.total += this.users.length;
  }

  async testUserLogin() {
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Logging in user ${i + 1}: ${user.email}`);
        
        const response = await this.api.login({
          email: user.email,
          password: user.password
        });

        // Validate response
        const validation = this.validator.validate(response, 'auth');
        if (!validation.isValid) {
          throw new Error(`Login validation failed: ${validation.errors.join(', ')}`);
        }

        // Update token (might be refreshed)
        user.token = response.data.token;
        
        this.logger.success(`User ${i + 1} logged in successfully`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} login failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} login: ${error.message}`);
        throw error;
      }
    }
    this.testResults.total += this.users.length;
  }

  async testWebSocketConnection() {
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Connecting WebSocket for user ${i + 1}`);
        
        const ws = new WebSocketClient(null, this.logger);
        await ws.connectAndAuthenticate(user.token);
        
        user.websocket = ws;
        this.websockets.push(ws);
        
        this.logger.success(`User ${i + 1} WebSocket connected and authenticated`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} WebSocket connection failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} WebSocket: ${error.message}`);
        throw error;
      }
    }
    this.testResults.total += this.users.length;
  }

  async testProfileUpdate() {
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Updating profile for user ${i + 1}`);
        
        // Set auth token
        this.api.setAuthToken(user.token);
        
        const response = await this.api.updateProfile(user.profileData);

        // Validate response
        const validation = this.validator.validate(response, 'auth');
        if (!validation.isValid) {
          throw new Error(`Profile update validation failed: ${validation.errors.join(', ')}`);
        }
        
        this.logger.success(`User ${i + 1} profile updated successfully`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} profile update failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} profile update: ${error.message}`);
        throw error;
      }
    }
    this.testResults.total += this.users.length;
  }

  async testAssessmentSubmission() {
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Submitting assessment for user ${i + 1}`);
        
        // Set auth token
        this.api.setAuthToken(user.token);
        
        const response = await this.api.submitAssessment(user.assessmentData);

        // Validate response
        const validation = this.validator.validate(response, 'assessment-submission');
        if (!validation.isValid) {
          throw new Error(`Assessment submission validation failed: ${validation.errors.join(', ')}`);
        }

        // Store job ID
        user.jobId = response.data.jobId;
        
        this.logger.success(`User ${i + 1} assessment submitted successfully. Job ID: ${user.jobId}`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} assessment submission failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} assessment: ${error.message}`);
        throw error;
      }
    }
    this.testResults.total += this.users.length;
  }

  async testWebSocketNotifications() {
    const timeout = parseInt(process.env.ASSESSMENT_TIMEOUT) || 300000;
    
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Waiting for assessment completion for user ${i + 1}`);
        
        const result = await user.websocket.waitForAssessmentCompletion(timeout);
        
        // Store result ID
        user.resultId = result.resultId;
        
        this.logger.success(`User ${i + 1} received assessment completion notification. Result ID: ${user.resultId}`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} WebSocket notification failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} notification: ${error.message}`);
        throw error;
      }
    }
    this.testResults.total += this.users.length;
  }

  async testPersonaRetrieval() {
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Retrieving persona profile for user ${i + 1}`);
        
        // Set auth token
        this.api.setAuthToken(user.token);
        
        const response = await this.api.getResult(user.resultId);

        // Validate response
        const validation = this.validator.validate(response, 'assessment-result');
        if (!validation.isValid) {
          throw new Error(`Persona retrieval validation failed: ${validation.errors.join(', ')}`);
        }

        // Store persona data
        user.personaProfile = response.data.persona_profile;
        
        this.logger.success(`User ${i + 1} persona profile retrieved successfully. Archetype: ${user.personaProfile.archetype}`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} persona retrieval failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} persona: ${error.message}`);
        throw error;
      }
    }
    this.testResults.total += this.users.length;
  }

  async testChatbotFunctionality() {
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Testing chatbot for user ${i + 1}`);
        
        // Set auth token
        this.api.setAuthToken(user.token);
        
        // Create conversation from assessment
        const convResponse = await this.api.createConversationFromAssessment({
          assessment_id: user.resultId,
          title: `E2E Test Chat - User ${i + 1}`,
          auto_start_message: true
        });

        // Validate conversation response
        const convValidation = this.validator.validate(convResponse, 'chatbot');
        if (!convValidation.isValid) {
          throw new Error(`Conversation creation validation failed: ${convValidation.errors.join(', ')}`);
        }

        user.conversationId = convResponse.data.conversation.id;

        // Send a test message
        const messageResponse = await this.api.sendMessage(user.conversationId, {
          content: user.chatMessages[0],
          content_type: "text"
        });

        // Validate message response
        const msgValidation = this.validator.validate(messageResponse, 'chatbot');
        if (!msgValidation.isValid) {
          throw new Error(`Message validation failed: ${msgValidation.errors.join(', ')}`);
        }
        
        this.logger.success(`User ${i + 1} chatbot test completed successfully`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`User ${i + 1} chatbot test failed:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} chatbot: ${error.message}`);
        // Don't throw here, continue with cleanup
      }
    }
    this.testResults.total += this.users.length;
  }

  async testCleanup() {
    // Disconnect WebSockets first
    this.websockets.forEach((ws, index) => {
      try {
        ws.disconnect();
        this.logger.info(`WebSocket ${index + 1} disconnected`);
      } catch (error) {
        this.logger.warn(`Failed to disconnect WebSocket ${index + 1}:`, error.message);
      }
    });

    // Delete user accounts
    for (let i = 0; i < this.users.length; i++) {
      const user = this.users[i];
      try {
        this.logger.info(`Deleting account for user ${i + 1}`);
        
        // Set auth token
        this.api.setAuthToken(user.token);
        
        // Delete conversation if exists
        if (user.conversationId) {
          await this.api.deleteConversation(user.conversationId);
          this.logger.info(`Conversation deleted for user ${i + 1}`);
        }

        // Delete assessment result if exists
        if (user.resultId) {
          await this.api.deleteResult(user.resultId);
          this.logger.info(`Assessment result deleted for user ${i + 1}`);
        }

        // Note: User account deletion endpoint not available in current API
        // This would typically be done via admin endpoint or database cleanup
        
        this.logger.success(`Cleanup completed for user ${i + 1}`);
        this.testResults.passed++;
        
      } catch (error) {
        this.logger.error(`Cleanup failed for user ${i + 1}:`, error.message);
        this.testResults.failed++;
        this.testResults.errors.push(`User ${i + 1} cleanup: ${error.message}`);
      }
    }
    this.testResults.total += this.users.length;
  }

  async emergencyCleanup() {
    this.logger.warn('Performing emergency cleanup...');
    
    // Disconnect all WebSockets
    this.websockets.forEach((ws, index) => {
      try {
        ws.disconnect();
      } catch (error) {
        // Ignore errors during emergency cleanup
      }
    });

    // Clear auth token
    this.api.setAuthToken(null);
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    // User summary
    if (this.users.length > 0) {
      this.logger.info('\nTest Users Summary:');
      this.users.forEach((user, index) => {
        this.logger.info(`User ${index + 1}:`);
        this.logger.info(`  Email: ${user.email}`);
        this.logger.info(`  ID: ${user.id || 'N/A'}`);
        this.logger.info(`  Job ID: ${user.jobId || 'N/A'}`);
        this.logger.info(`  Result ID: ${user.resultId || 'N/A'}`);
        this.logger.info(`  Archetype: ${user.personaProfile?.archetype || 'N/A'}`);
        this.logger.info(`  Conversation ID: ${user.conversationId || 'N/A'}`);
      });
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All tests passed! E2E test suite completed successfully.');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} test(s) failed. Please check the errors above.`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const runner = new E2ETestRunner();
  runner.runTests().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = E2ETestRunner;
