#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');
const TestDataGenerator = require('./lib/test-data');
const APIClient = require('./lib/api-client');
const ResponseValidator = require('./lib/validators');

class ErrorScenariosTest {
  constructor() {
    this.logger = new Logger();
    this.testData = new TestDataGenerator();
    this.validator = new ResponseValidator(this.logger);
    this.api = new APIClient(null, this.logger);
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      errors: []
    };
  }

  async runTest() {
    this.logger.header('ATMA Error Scenarios Test');
    
    try {
      // Test authentication errors
      await this.testAuthenticationErrors();

      // Test validation errors
      await this.testValidationErrors();

      // Test authorization errors
      await this.testAuthorizationErrors();

      // Test rate limiting (if applicable)
      await this.testRateLimiting();

      // Summary
      this.printTestSummary();

    } catch (error) {
      this.logger.error('Test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(error.message);
    }
  }

  async testAuthenticationErrors() {
    this.logger.step(1, 4, 'Testing Authentication Errors');
    
    try {
      // Test invalid login credentials
      await this.testErrorScenario('Invalid login credentials', async () => {
        try {
          await this.api.login({
            email: '<EMAIL>',
            password: 'wrongpassword'
          });
          throw new Error('Should have failed with invalid credentials');
        } catch (error) {
          if (error.response && error.response.status === 401) {
            this.validateErrorResponse(error.response.data);
            return error.response.data;
          }
          throw error;
        }
      });

      // Test missing authorization header
      await this.testErrorScenario('Missing authorization header', async () => {
        const originalToken = this.api.client.defaults.headers.common['Authorization'];
        delete this.api.client.defaults.headers.common['Authorization'];
        
        try {
          await this.api.getProfile();
          throw new Error('Should have failed without authorization');
        } catch (error) {
          if (error.response && error.response.status === 401) {
            this.validateErrorResponse(error.response.data);
            return error.response.data;
          }
          throw error;
        } finally {
          if (originalToken) {
            this.api.client.defaults.headers.common['Authorization'] = originalToken;
          }
        }
      });

      // Test invalid token
      await this.testErrorScenario('Invalid token', async () => {
        const originalToken = this.api.client.defaults.headers.common['Authorization'];
        this.api.setAuthToken('invalid.token.here');
        
        try {
          await this.api.getProfile();
          throw new Error('Should have failed with invalid token');
        } catch (error) {
          if (error.response && error.response.status === 401) {
            this.validateErrorResponse(error.response.data);
            return error.response.data;
          }
          throw error;
        } finally {
          if (originalToken) {
            this.api.client.defaults.headers.common['Authorization'] = originalToken;
          }
        }
      });

      this.logger.success('Authentication errors test completed');
      
    } catch (error) {
      this.logger.error('Authentication errors test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Auth errors: ${error.message}`);
    }
  }

  async testValidationErrors() {
    this.logger.step(2, 4, 'Testing Validation Errors');
    
    try {
      // Test invalid email format
      await this.testErrorScenario('Invalid email format', async () => {
        try {
          await this.api.register({
            email: 'invalid-email',
            password: 'ValidPassword123!',
            username: 'testuser'
          });
          throw new Error('Should have failed with invalid email');
        } catch (error) {
          if (error.response && error.response.status === 400) {
            this.validateErrorResponse(error.response.data);
            return error.response.data;
          }
          throw error;
        }
      });

      // Test missing required fields
      await this.testErrorScenario('Missing required fields', async () => {
        try {
          await this.api.register({
            email: '<EMAIL>'
            // Missing password and username
          });
          throw new Error('Should have failed with missing fields');
        } catch (error) {
          if (error.response && error.response.status === 400) {
            this.validateErrorResponse(error.response.data);
            return error.response.data;
          }
          throw error;
        }
      });

      this.logger.success('Validation errors test completed');
      
    } catch (error) {
      this.logger.error('Validation errors test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Validation errors: ${error.message}`);
    }
  }

  async testAuthorizationErrors() {
    this.logger.step(3, 4, 'Testing Authorization Errors');
    
    try {
      // Create a regular user for testing
      const userData = this.testData.generateUserRegistrationData();
      const registerResponse = await this.api.register(userData);
      const userToken = registerResponse.data.token;

      // Test admin endpoint access with user token
      await this.testErrorScenario('User accessing admin endpoint', async () => {
        this.api.setAuthToken(userToken);
        
        try {
          await this.api.adminLogin({
            username: 'admin',
            password: 'admin123'
          });
          throw new Error('Should have failed accessing admin endpoint');
        } catch (error) {
          if (error.response && error.response.status === 403) {
            this.validateErrorResponse(error.response.data);
            return error.response.data;
          }
          throw error;
        }
      });

      this.logger.success('Authorization errors test completed');
      
    } catch (error) {
      this.logger.error('Authorization errors test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Authorization errors: ${error.message}`);
    }
  }

  async testRateLimiting() {
    this.logger.step(4, 4, 'Testing Rate Limiting');
    
    try {
      this.logger.info('Rate limiting test skipped - requires high volume requests');
      this.testResults.passed++;
      
    } catch (error) {
      this.logger.error('Rate limiting test failed:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Rate limiting: ${error.message}`);
    }
  }

  async testErrorScenario(name, testFunction) {
    this.testResults.total++;
    
    try {
      this.logger.info(`Testing error scenario: ${name}`);
      const response = await testFunction();
      
      this.logger.success(`${name}: PASSED`);
      this.testResults.passed++;
      return response;
      
    } catch (error) {
      this.logger.error(`${name}: FAILED - ${error.message}`);
      this.testResults.failed++;
      this.testResults.errors.push(`${name}: ${error.message}`);
      throw error;
    }
  }

  validateErrorResponse(response) {
    // Validate error response format according to API Gateway spec
    if (!response.success || response.success !== false) {
      throw new Error('Error response should have success: false');
    }

    if (!response.error) {
      throw new Error('Error response missing error object');
    }

    if (!response.error.code) {
      throw new Error('Error response missing error.code');
    }

    if (!response.error.message) {
      throw new Error('Error response missing error.message');
    }

    if (!response.error.timestamp) {
      throw new Error('Error response missing error.timestamp');
    }

    this.logger.debug('Error response format validated successfully');
  }

  printTestSummary() {
    this.logger.separator();
    this.logger.header('ERROR SCENARIOS TEST SUMMARY');
    
    const summary = {
      'Total Tests': this.testResults.total,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.total) * 100).toFixed(2)}%`
    };
    
    this.logger.table(summary, 'Test Results');
    
    if (this.testResults.errors.length > 0) {
      this.logger.error('Errors encountered:');
      this.testResults.errors.forEach((error, index) => {
        this.logger.error(`${index + 1}. ${error}`);
      });
    }

    this.logger.separator();
    
    if (this.testResults.failed === 0) {
      this.logger.success('🎉 All error scenario tests passed!');
    } else {
      this.logger.error(`❌ ${this.testResults.failed} test(s) failed. Please check the errors above.`);
    }
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  const test = new ErrorScenariosTest();
  test.runTest().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = ErrorScenariosTest;
