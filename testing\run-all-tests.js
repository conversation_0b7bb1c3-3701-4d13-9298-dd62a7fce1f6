#!/usr/bin/env node

require('dotenv').config();
const Logger = require('./lib/logger');

// Import all test modules
const ComprehensiveAPITest = require('./comprehensive-api-test');
const WebSocketComprehensiveTest = require('./websocket-comprehensive-test');
const ErrorScenariosTest = require('./error-scenarios-test');
const HeaderValidationTest = require('./header-validation-test');
const WebSocketNotificationValidationTest = require('./websocket-notification-validation-test');

class AllTestsRunner {
  constructor() {
    this.logger = new Logger();
    this.testSuites = [
      { name: 'Header Validation Test', class: HeaderValidationTest, critical: true },
      { name: 'WebSocket Notification Validation Test', class: WebSocketNotificationValidationTest, critical: true },
      { name: 'Comprehensive API Test', class: ComprehensiveAPITest, critical: false },
      { name: 'WebSocket Comprehensive Test', class: WebSocketComprehensiveTest, critical: false },
      { name: 'Error <PERSON>ena<PERSON>s Test', class: ErrorScenariosTest, critical: false }
    ];
    
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      critical_failed: 0,
      suite_results: []
    };
  }

  async runAllTests() {
    this.logger.header('ATMA Complete Test Suite - API Gateway & WebSocket Specification Compliance');
    this.logger.info(`Running ${this.testSuites.length} test suites...`);
    this.logger.separator();

    for (let i = 0; i < this.testSuites.length; i++) {
      const suite = this.testSuites[i];
      await this.runTestSuite(suite, i + 1);
      
      // Add delay between test suites to avoid overwhelming the system
      if (i < this.testSuites.length - 1) {
        this.logger.info('Waiting 5 seconds before next test suite...');
        await this.delay(5000);
      }
    }

    this.printFinalSummary();
    return this.results.failed === 0;
  }

  async runTestSuite(suite, index) {
    this.logger.header(`Test Suite ${index}/${this.testSuites.length}: ${suite.name}`);
    
    const startTime = Date.now();
    let success = false;
    let error = null;

    try {
      const testInstance = new suite.class();
      await testInstance.runTest();
      
      // Check if the test instance has results
      if (testInstance.testResults) {
        success = testInstance.testResults.failed === 0;
        this.results.passed += testInstance.testResults.passed;
        this.results.failed += testInstance.testResults.failed;
        this.results.total += testInstance.testResults.total;
      } else {
        // Assume success if no explicit results
        success = true;
        this.results.passed++;
        this.results.total++;
      }

    } catch (err) {
      success = false;
      error = err.message;
      this.results.failed++;
      this.results.total++;
      
      if (suite.critical) {
        this.results.critical_failed++;
      }
      
      this.logger.error(`Test suite failed: ${error}`);
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    this.results.suite_results.push({
      name: suite.name,
      success,
      duration,
      critical: suite.critical,
      error
    });

    if (success) {
      this.logger.success(`✅ ${suite.name} completed successfully in ${duration}s`);
    } else {
      const criticality = suite.critical ? ' (CRITICAL)' : '';
      this.logger.error(`❌ ${suite.name} failed in ${duration}s${criticality}`);
    }

    this.logger.separator();
  }

  printFinalSummary() {
    this.logger.header('Final Test Results Summary');
    
    // Overall statistics
    this.logger.info(`Total Test Suites: ${this.testSuites.length}`);
    this.logger.info(`Total Individual Tests: ${this.results.total}`);
    this.logger.success(`Passed: ${this.results.passed}`);
    
    if (this.results.failed > 0) {
      this.logger.error(`Failed: ${this.results.failed}`);
      if (this.results.critical_failed > 0) {
        this.logger.error(`Critical Failures: ${this.results.critical_failed}`);
      }
    }

    const successRate = this.results.total > 0 ? 
      ((this.results.passed / this.results.total) * 100).toFixed(1) : 0;
    this.logger.info(`Overall Success Rate: ${successRate}%`);

    this.logger.separator();

    // Detailed suite results
    this.logger.info('Test Suite Details:');
    this.results.suite_results.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      const criticality = result.critical ? ' [CRITICAL]' : '';
      const error = result.error ? ` - ${result.error}` : '';
      
      this.logger.info(`  ${index + 1}. ${result.name}: ${status} (${result.duration}s)${criticality}${error}`);
    });

    this.logger.separator();

    // Compliance assessment
    this.assessCompliance();
  }

  assessCompliance() {
    this.logger.header('API Gateway & WebSocket Specification Compliance Assessment');
    
    const criticalTests = this.results.suite_results.filter(r => r.critical);
    const criticalPassed = criticalTests.filter(r => r.success).length;
    const criticalTotal = criticalTests.length;
    
    this.logger.info('Critical Compliance Tests:');
    this.logger.info(`  Header Validation: ${this.getTestStatus('Header Validation Test')}`);
    this.logger.info(`  WebSocket Notification Validation: ${this.getTestStatus('WebSocket Notification Validation Test')}`);
    
    if (criticalPassed === criticalTotal) {
      this.logger.success('🎉 COMPLIANCE STATUS: FULLY COMPLIANT');
      this.logger.success('✅ All critical specification requirements are met');
      this.logger.success('✅ API Gateway headers are properly implemented');
      this.logger.success('✅ WebSocket notifications follow specification');
    } else {
      this.logger.error('❌ COMPLIANCE STATUS: NON-COMPLIANT');
      this.logger.error(`❌ ${criticalTotal - criticalPassed}/${criticalTotal} critical tests failed`);
      this.logger.error('❌ Specification requirements are not fully met');
    }

    this.logger.separator();

    // Recommendations
    if (this.results.failed > 0) {
      this.logger.info('Recommendations:');
      this.results.suite_results.forEach(result => {
        if (!result.success) {
          if (result.critical) {
            this.logger.error(`  🔴 HIGH PRIORITY: Fix ${result.name}`);
          } else {
            this.logger.warn(`  🟡 MEDIUM PRIORITY: Fix ${result.name}`);
          }
        }
      });
    } else {
      this.logger.success('🎉 No issues found! The system is fully compliant with specifications.');
    }
  }

  getTestStatus(testName) {
    const result = this.results.suite_results.find(r => r.name === testName);
    return result ? (result.success ? '✅ PASS' : '❌ FAIL') : '❓ NOT RUN';
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run all tests if this file is executed directly
if (require.main === module) {
  const runner = new AllTestsRunner();
  runner.runAllTests()
    .then((success) => {
      if (success) {
        console.log('\n🎉 All tests completed successfully!');
        process.exit(0);
      } else {
        console.log('\n❌ Some tests failed. Check the output above for details.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = AllTestsRunner;
