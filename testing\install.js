#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Installing ATMA E2E Testing Suite...\n');

try {
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    console.error('❌ Node.js 18 or higher is required. Current version:', nodeVersion);
    process.exit(1);
  }
  
  console.log('✅ Node.js version check passed:', nodeVersion);
  
  // Install dependencies
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  console.log('✅ Dependencies installed successfully');
  
  // Create results directories
  console.log('📁 Creating results directories...');
  const resultsDir = path.join(__dirname, 'results');
  const logsDir = path.join(resultsDir, 'logs');
  const responsesDir = path.join(resultsDir, 'responses');
  
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }
  
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  
  if (!fs.existsSync(responsesDir)) {
    fs.mkdirSync(responsesDir, { recursive: true });
  }
  
  console.log('✅ Results directories created');
  
  // Check .env file
  const envPath = path.join(__dirname, '.env');
  if (!fs.existsSync(envPath)) {
    console.log('⚠️  .env file not found, using defaults');
  } else {
    console.log('✅ .env file found');
  }
  
  // Test basic functionality
  console.log('🧪 Testing basic functionality...');
  
  try {
    // Test logger
    const Logger = require('./lib/logger');
    const logger = new Logger({ saveToFile: false });
    logger.info('Logger test successful');
    
    // Test data generator
    const TestDataGenerator = require('./lib/test-data');
    const testData = new TestDataGenerator();
    const user = testData.generateTestUser(1);
    if (!user.email || !user.password) {
      throw new Error('Test data generation failed');
    }
    
    console.log('✅ Basic functionality test passed');
    
  } catch (error) {
    console.error('❌ Basic functionality test failed:', error.message);
    process.exit(1);
  }
  
  // Display usage information
  console.log('\n🎉 Installation completed successfully!\n');
  
  console.log('📋 Quick Start:');
  console.log('  npm test                 # Run complete E2E test suite');
  console.log('  npm run test:single      # Run single user test');
  console.log('  npm run test:dual        # Run dual user test');
  console.log('  npm run test:websocket   # Test WebSocket only');
  console.log('  npm run test:chatbot     # Test chatbot only');
  console.log('  npm run test:cleanup     # Clean up test users');
  
  console.log('\n⚙️  Configuration:');
  console.log('  Edit .env file to customize settings');
  console.log('  Set API_BASE_URL and WEBSOCKET_URL for your environment');
  
  console.log('\n📖 Documentation:');
  console.log('  See README.md for detailed usage instructions');
  console.log('  Check results/ directory for test logs and responses');
  
  console.log('\n🔧 Prerequisites:');
  console.log('  - ATMA backend services must be running');
  console.log('  - API Gateway accessible at localhost:3000 (or configured URL)');
  console.log('  - WebSocket service accessible for real-time notifications');
  
  console.log('\n✨ Ready to test! Run "npm test" to start.\n');
  
} catch (error) {
  console.error('❌ Installation failed:', error.message);
  process.exit(1);
}
